import React, { useState, useEffect } from 'react';
import DataTable from './DataTable';
import Modal from '../ui/Modal';
import * as XLSX from 'xlsx';
import { formatToIST } from "../../utils/dateUtils";

export default function Orders() {
  const [isLoading, setIsLoading] = useState(true);
  const [orders, setOrders] = useState([]);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [orderItems, setOrderItems] = useState([]);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [error, setError] = useState(null);
  const [statusCounts, setStatusCounts] = useState({
    total: 0,
    delivered: 0,
    processing: 0,
    cancelled: 0
  });
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [orderToDelete, setOrderToDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Status filter options
  const statusFilters = [
    { id: 'all', label: 'All Orders' },
    { id: 'placed', label: 'Placed' },
    { id: 'processing', label: 'Processing' },
    { id: 'shipped', label: 'Shipped' },
    { id: 'delivered', label: 'Delivered' },
    { id: 'cancelled', label: 'Cancelled' }
  ];

  const ITEMS_PER_PAGE = 10;

  // Fetch orders data
  useEffect(() => {
    fetchOrders();
  }, [currentPage, activeTab, searchTerm]);

  // Fetch orders from the API
  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const status = activeTab !== 'all' ? activeTab : '';

      // Call our API client to get orders with pagination and filtering
      const result = await window.ApiClient.getAdminOrders(
        currentPage,
        ITEMS_PER_PAGE,
        status,
        searchTerm
      );

      // Set orders from the response
      setOrders(result.orders || []);

      // Set total count from pagination data
      if (result.pagination && typeof result.pagination.total === 'number') {
        setTotalCount(result.pagination.total);
      } else {
        // Fallback to length of orders array if pagination data is missing
        setTotalCount(result.orders?.length || 0);
      }

      // Update status counts
      updateStatusCounts();

    } catch (error) {
      console.error('Error fetching orders:', error);
      setError('Failed to load orders. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Update counts for different order statuses
  const updateStatusCounts = async () => {
    try {
      // Get dashboard stats to get order status counts
      const statsResponse = await window.ApiClient.getAdminDashboardStats('month');

      if (statsResponse.success && statsResponse.stats) {
        const { ordersByStatus = [], totalOrders = 0 } = statsResponse.stats;

        // Calculate counts by status
        const processingCount = ordersByStatus
          .filter(item => ['placed', 'processing', 'shipped'].includes(item.status))
          .reduce((sum, item) => sum + item.count, 0);

        const deliveredCount = ordersByStatus
          .find(item => item.status === 'delivered')?.count || 0;

        const cancelledCount = ordersByStatus
          .find(item => item.status === 'cancelled')?.count || 0;

        setStatusCounts({
          total: totalOrders,
          delivered: deliveredCount,
          processing: processingCount,
          cancelled: cancelledCount
        });
      }
    } catch (error) {
      console.error('Error fetching status counts:', error);
    }
  };

  // Fetch order details
  const fetchOrderDetails = async (orderId) => {
    try {
      const orderDetails = await window.ApiClient.getAdminOrderDetails(orderId);

      if (orderDetails.order) {
        return {
          ...orderDetails.order,
          items: orderDetails.items || []
        };
      }

      throw new Error('Order details not found');
    } catch (error) {
      console.error('Error fetching order details:', error);
      return null;
    }
  };

  // Format currency values
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format dates
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return formatToIST(dateString);
  };

  // Handle order click/view
  const handleViewOrder = (order) => {
    // Navigate to the individual order page
    window.location.href = `/admin/order/${order.id}`;
  };

  // Handle status change
  const handleStatusChange = async (orderId, newStatus) => {
    try {
      setIsUpdatingStatus(true);

      // Update order status via the API
      const result = await window.ApiClient.updateOrderStatus(orderId, newStatus);

      if (result.success) {
        // Update the local state for the selected order
        if (selectedOrder && selectedOrder.id === orderId) {
          setSelectedOrder(prev => ({
            ...prev,
            order_status: newStatus
          }));
        }

        // Update the orders list if the order is in the current view
        setOrders(prevOrders =>
          prevOrders.map(order =>
            order.id === orderId ? { ...order, order_status: newStatus } : order
          )
        );

        // Refetch the counts
        updateStatusCounts();
      } else {
        throw new Error(result.message || 'Failed to update status');
      }
    } catch (error) {
      console.error(`Error updating order ${orderId} status:`, error);
      setError(`Failed to update order status: ${error.message}`);
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedOrder(null);
    setOrderItems([]);
  };

  // Open delete confirmation modal
  const openDeleteModal = (order) => {
    setOrderToDelete(order);
    setIsDeleteModalOpen(true);
  };

  // Close delete confirmation modal
  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setOrderToDelete(null);
  };

  // Handle order deletion
  const handleDeleteOrder = async () => {
    if (!orderToDelete) return;

    try {
      setIsDeleting(true);
      setError(null);

      // Call API to delete the order
      const result = await window.ApiClient.deleteOrder(orderToDelete.id);

      if (result.success) {
        // Remove the order from the list
        setOrders(prevOrders => prevOrders.filter(order => order.id !== orderToDelete.id));

        // Update status counts
        updateStatusCounts();

        // Close the modal
        closeDeleteModal();
      } else {
        throw new Error(result.error || 'Failed to delete order');
      }
    } catch (error) {
      console.error('Error deleting order:', error);
      setError(`Failed to delete order: ${error.message}`);
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  // Handle search
  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page on new search
  };

  // Handle tab/filter change
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    setCurrentPage(1); // Reset to first page on new filter
  };

  // Download orders as XLSX
  const [dateRange, setDateRange] = useState({
    fromDate: '',
    toDate: ''
  });
  const [showDateRangeModal, setShowDateRangeModal] = useState(false);

  const downloadOrdersXLSX = async (useCustomDateRange = false) => {
    try {
      setIsLoading(true);

      let allOrders = [];

      if (useCustomDateRange && (dateRange.fromDate || dateRange.toDate)) {
        // Fetch orders with date range using API
        const result = await window.ApiClient.getAdminOrdersByDateRange(
          dateRange.fromDate,
          dateRange.toDate,
          activeTab !== 'all' ? activeTab : '',
          searchTerm
        );
        allOrders = result.orders || [];
      } else {
        // Fetch all orders for the current filter (not paginated)
        const status = activeTab !== 'all' ? activeTab : '';
        const result = await window.ApiClient.getAdminOrders(1, 10000, status, searchTerm);
        allOrders = result.orders || [];
      }

      if (allOrders.length === 0) {
        setError('No orders found to export');
        return;
      }

      // Fetch order items for all orders
      const orderIds = allOrders.map(order => order.id);
      const itemsResult = await window.ApiClient.getAdminOrderItems(orderIds);
      const orderItems = itemsResult.orderItems || [];

      // Group items by order ID
      const itemsByOrder = {};
      orderItems.forEach(item => {
        if (!itemsByOrder[item.order_id]) {
          itemsByOrder[item.order_id] = [];
        }
        itemsByOrder[item.order_id].push(item);
      });

      // Create detailed orders export with item information
      const orderExportData = [];

      allOrders.forEach(order => {
        const items = itemsByOrder[order.id] || [];

        if (items.length === 0) {
          // Order with no items - create a single row
          orderExportData.push({
            'Order Number': order.order_number || '',
            'Customer Name': order.user_name || '',
            'Customer Email': order.user_email || '',
            'Customer Phone': order.user_phone || '',
            'Order Date': formatDate(order.created_at),
            'Total Amount': order.total_amount || 0,
            'Delivery Location': order.location_address || '',
            'Order Status': order.order_status ? order.order_status.charAt(0).toUpperCase() + order.order_status.slice(1) : '',
            'Payment Status': order.payment_status ? order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1) : 'Unknown',
            'Payment Method': order.payment_method || '',
            'Shipping Name': order.shipping_full_name || '',
            'Shipping Phone': order.shipping_phone || '',
            // 'Shipping Address': order.shipping_address || '',
            // 'Shipping City': order.shipping_city || '',
            'Shipping Zip': order.shipping_zip_code || '',
            // 'Shipping Instructions': order.shipping_instructions || '',
            'Delivery Fee': order.delivery_fee || 0,
            'Discount': order.discount_amount || 0,
            'Coupon Code': order.coupon_code || '',
            'Items Count': 0,
            // Item details (empty for orders with no items)
            'Product Name': '',
            'Product Category': '',
            'Item Quantity': '',
            'Item Unit Price': '',
            'Item Total Price': ''
          });
        } else {
          // Order with items - create a row for each item
          items.forEach((item, index) => {
            orderExportData.push({
              'Order Number': index === 0 ? order.order_number || '' : '', // Only show order details on first item
              'Customer Name': index === 0 ? order.user_name || '' : '',
              'Customer Email': index === 0 ? order.user_email || '' : '',
              'Customer Phone': index === 0 ? order.user_phone || '' : '',
              'Order Date': index === 0 ? formatDate(order.created_at) : '',
              'Total Amount': index === 0 ? order.total_amount || 0 : '',
              'Delivery Location': index === 0 ? order.location_address || '' : '',
              'Order Status': index === 0 ? (order.order_status ? order.order_status.charAt(0).toUpperCase() + order.order_status.slice(1) : '') : '',
              'Payment Status': index === 0 ? (order.payment_status ? order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1) : 'Unknown') : '',
              'Payment Method': index === 0 ? order.payment_method || '' : '',
              'Shipping Name': index === 0 ? order.shipping_full_name || '' : '',
              'Shipping Phone': index === 0 ? order.shipping_phone || '' : '',
              // 'Shipping Address': index === 0 ? order.shipping_address || '' : '',
              // 'Shipping City': index === 0 ? order.shipping_city || '' : '',
              'Shipping Zip': index === 0 ? order.shipping_zip_code || '' : '',
              // 'Shipping Instructions': index === 0 ? order.shipping_instructions || '' : '',
              'Delivery Fee': index === 0 ? order.delivery_fee || 0 : '',
              'Discount': index === 0 ? order.discount_amount || 0 : '',
              'Coupon Code': index === 0 ? order.coupon_code || '' : '',
              'Items Count': index === 0 ? items.length : '',
              // Item details
              'Product Name': item.product_name || '',
              'Product Category': item.category_name || '',
              'Item Quantity': item.quantity || 0,
              'Item Unit Price': item.product_price || 0,
              'Item Total Price': item.total_price || (item.quantity * item.product_price) || 0
            });
          });
        }
      });

      // Create order items export data
      const itemsExportData = [];
      allOrders.forEach(order => {
        const items = itemsByOrder[order.id] || [];
        items.forEach(item => {
          itemsExportData.push({
            'Order Number': order.order_number || '',
            'Order Date': formatDate(order.created_at),
            'Customer Name': order.user_name || '',
            'Product Name': item.product_name || '',
            'Category': item.category_name || '',
            'Quantity': item.quantity || 0,
            'Unit Price': item.product_price || 0,
            'Total Price': item.total_price || (item.quantity * item.product_price) || 0,
            'Order Status': order.order_status ? order.order_status.charAt(0).toUpperCase() + order.order_status.slice(1) : '',
            'Payment Status': order.payment_status ? order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1) : 'Unknown'
          });
        });
      });

      // Create workbook with detailed orders (including item details in main sheet)
      const workbook = XLSX.utils.book_new();

      // Main Orders worksheet with item details
      const ordersWorksheet = XLSX.utils.json_to_sheet(orderExportData);
      const ordersColumnWidths = [
        { wch: 15 }, // Order Number
        { wch: 20 }, // Customer Name
        { wch: 25 }, // Customer Email
        { wch: 15 }, // Customer Phone
        { wch: 15 }, // Order Date
        { wch: 12 }, // Total Amount
        { wch: 30 }, // Delivery Location
        { wch: 12 }, // Order Status
        { wch: 15 }, // Payment Status
        { wch: 15 }, // Payment Method
        { wch: 20 }, // Shipping Name
        { wch: 15 }, // Shipping Phone
        // { wch: 40 }, // Shipping Address
        // { wch: 15 }, // Shipping City
        { wch: 10 }, // Shipping Zip
        // { wch: 25 }, // Shipping Instructions
        { wch: 12 }, // Delivery Fee
        { wch: 10 }, // Discount
        { wch: 15 }, // Coupon Code
        { wch: 10 }, // Items Count
        { wch: 30 }, // Product Name
        { wch: 15 }, // Product Category
        { wch: 10 }, // Item Quantity
        { wch: 12 }, // Item Unit Price
        { wch: 12 }  // Item Total Price
      ];
      ordersWorksheet['!cols'] = ordersColumnWidths;
      XLSX.utils.book_append_sheet(workbook, ordersWorksheet, 'Orders with Items');

      // Optional: Keep separate Order Items worksheet for reference
      if (itemsExportData.length > 0) {
        const itemsWorksheet = XLSX.utils.json_to_sheet(itemsExportData);
        const itemsColumnWidths = [
          { wch: 15 }, // Order Number
          { wch: 15 }, // Order Date
          { wch: 20 }, // Customer Name
          { wch: 30 }, // Product Name
          { wch: 15 }, // Category
          { wch: 10 }, // Quantity
          { wch: 12 }, // Unit Price
          { wch: 12 }, // Total Price
          { wch: 12 }, // Order Status
          { wch: 15 }  // Payment Status
        ];
        itemsWorksheet['!cols'] = itemsColumnWidths;
        XLSX.utils.book_append_sheet(workbook, itemsWorksheet, 'Items Summary');
      }

      // Generate filename with current date and filter
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0];
      const filterStr = activeTab !== 'all' ? `_${activeTab}` : '';
      const searchStr = searchTerm ? `_search` : '';
      let dateRangeStr = '';

      if (useCustomDateRange && (dateRange.fromDate || dateRange.toDate)) {
        if (dateRange.fromDate && dateRange.toDate) {
          dateRangeStr = `_${dateRange.fromDate}_to_${dateRange.toDate}`;
        } else if (dateRange.fromDate) {
          dateRangeStr = `_from_${dateRange.fromDate}`;
        } else if (dateRange.toDate) {
          dateRangeStr = `_until_${dateRange.toDate}`;
        }
      }

      const filename = `orders${filterStr}${searchStr}${dateRangeStr}_${dateStr}.xlsx`;

      // Save file
      XLSX.writeFile(workbook, filename);

      // Close modal if it was open
      setShowDateRangeModal(false);
      setError(null);

    } catch (error) {
      console.error('Error downloading orders:', error);
      setError('Failed to download orders. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Column configuration for the DataTable
  const columns = [
    { key: 'order_number', label: 'Order #' },
    { key: 'user_name', label: 'Customer' },
    { key: 'created_at', label: 'Date', render: (row) => formatDate(row.created_at) },
    { key: 'total_amount', label: 'Total', render: (row) => formatCurrency(row.total_amount) },
    {
      key: 'location',
      label: 'Delivery Location',
      render: (row) => (
        <span>
          {row.location_address ? row.location_address : 'N/A'}
        </span>
      )
    },
    {
      key: 'order_status',
      label: 'Status',
      render: (row) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${row.order_status === 'delivered' ? 'bg-green-100 text-green-800' :
            row.order_status === 'processing' ? 'bg-blue-100 text-blue-800' :
              row.order_status === 'shipped' ? 'bg-indigo-100 text-indigo-800' :
                row.order_status === 'cancelled' ? 'bg-red-100 text-red-800' :
                  'bg-purple-100 text-purple-800'
          }`}>
          {row.order_status.charAt(0).toUpperCase() + row.order_status.slice(1)}
        </span>
      )
    },
    {
      key: 'payment_status',
      label: 'Payment',
      render: (row) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${row.payment_status === 'paid' ? 'bg-green-100 text-green-800' :
            row.payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
          }`}>
          {row.payment_status?.charAt(0).toUpperCase() + row.payment_status?.slice(1) || 'Unknown'}
        </span>
      )
    },

  ];

  // Show error message if there's an error
  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <span className="material-icons-round text-red-400">error</span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
            <div className="mt-2">
              <button
                onClick={() => {
                  setError(null);
                  fetchOrders();
                }}
                className="px-3 py-1 text-sm font-medium text-red-800 bg-red-100 rounded-md hover:bg-red-200"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <Modal
          title="Delete Order"
          onClose={closeDeleteModal}
        >
          <div className="p-6">
            <div className="mb-4">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4">
                <span className="material-icons-round text-red-600">delete</span>
              </div>
              <h3 className="text-lg font-medium text-center text-gray-900 mb-2">
                Confirm Order Deletion
              </h3>
              <p className="text-sm text-gray-500 text-center">
                Are you sure you want to delete order #{orderToDelete?.order_number}? This action cannot be undone.
              </p>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={closeDeleteModal}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
                disabled={isDeleting}
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteOrder}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Deleting...
                  </span>
                ) : (
                  'Delete Order'
                )}
              </button>
            </div>
          </div>
        </Modal>
      )}

      {/* Date Range Modal */}
      {showDateRangeModal && (
        <Modal
          title="Download Orders by Date Range"
          onClose={() => setShowDateRangeModal(false)}
        >
          <div className="p-6">
            <div className="mb-6">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-green-100 rounded-full mb-4">
                <span className="material-icons-round text-green-600">date_range</span>
              </div>
              <h3 className="text-lg font-medium text-center text-gray-900 mb-2">
                Select Date Range
              </h3>
              <p className="text-sm text-gray-500 text-center mb-6">
                Choose the date range for orders you want to export. Leave blank to include all dates.
              </p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    From Date
                  </label>
                  <input
                    type="date"
                    value={dateRange.fromDate}
                    onChange={(e) => setDateRange(prev => ({ ...prev, fromDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    To Date
                  </label>
                  <input
                    type="date"
                    value={dateRange.toDate}
                    onChange={(e) => setDateRange(prev => ({ ...prev, toDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>

                {/* Quick date presets */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Quick Select
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => {
                        const today = new Date();
                        const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                        setDateRange({
                          fromDate: lastWeek.toISOString().split('T')[0],
                          toDate: today.toISOString().split('T')[0]
                        });
                      }}
                      className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                    >
                      Last 7 Days
                    </button>
                    <button
                      onClick={() => {
                        const today = new Date();
                        const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
                        setDateRange({
                          fromDate: lastMonth.toISOString().split('T')[0],
                          toDate: today.toISOString().split('T')[0]
                        });
                      }}
                      className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                    >
                      Last 30 Days
                    </button>
                    <button
                      onClick={() => {
                        const today = new Date();
                        const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
                        setDateRange({
                          fromDate: thisMonth.toISOString().split('T')[0],
                          toDate: today.toISOString().split('T')[0]
                        });
                      }}
                      className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                    >
                      This Month
                    </button>
                    <button
                      onClick={() => {
                        const today = new Date();
                        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
                        setDateRange({
                          fromDate: lastMonth.toISOString().split('T')[0],
                          toDate: lastMonthEnd.toISOString().split('T')[0]
                        });
                      }}
                      className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                    >
                      Last Month
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowDateRangeModal(false);
                  setDateRange({ fromDate: '', toDate: '' });
                }}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                onClick={() => downloadOrdersXLSX(true)}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Downloading...
                  </span>
                ) : (
                  <>
                    <span className="material-icons-round mr-2">download</span>
                    Download
                  </>
                )}
              </button>
            </div>
          </div>
        </Modal>
      )}

      {/* Header Section with Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm p-4 flex items-center border border-gray-200">
          <div className="rounded-full bg-blue-50 p-3 mr-4">
            <span className="material-icons-round text-blue-600">receipt_long</span>
          </div>
          <div>
            <p className="text-sm text-gray-500">Total Orders</p>
            <p className="text-xl font-semibold">
              {isLoading ? (
                <span className="text-gray-300">...</span>
              ) : (
                statusCounts.total
              )}
            </p>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 flex items-center border border-gray-200">
          <div className="rounded-full bg-green-50 p-3 mr-4">
            <span className="material-icons-round text-green-600">check_circle</span>
          </div>
          <div>
            <p className="text-sm text-gray-500">Completed</p>
            <p className="text-xl font-semibold">
              {isLoading ? (
                <span className="text-gray-300">...</span>
              ) : (
                statusCounts.delivered
              )}
            </p>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 flex items-center border border-gray-200">
          <div className="rounded-full bg-yellow-50 p-3 mr-4">
            <span className="material-icons-round text-yellow-600">pending</span>
          </div>
          <div>
            <p className="text-sm text-gray-500">Processing</p>
            <p className="text-xl font-semibold">
              {isLoading ? (
                <span className="text-gray-300">...</span>
              ) : (
                statusCounts.processing
              )}
            </p>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 flex items-center border border-gray-200">
          <div className="rounded-full bg-red-50 p-3 mr-4">
            <span className="material-icons-round text-red-600">cancel</span>
          </div>
          <div>
            <p className="text-sm text-gray-500">Cancelled</p>
            <p className="text-xl font-semibold">
              {isLoading ? (
                <span className="text-gray-300">...</span>
              ) : (
                statusCounts.cancelled
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div className="flex flex-col md:flex-row justify-between mb-4">
          {/* Status Tabs */}
          <div className="flex items-center space-x-2 overflow-x-auto pb-2 md:pb-0 mb-3 md:mb-0">
            {statusFilters.map(filter => (
              <button
                key={filter.id}
                onClick={() => handleTabChange(filter.id)}
                className={`px-3 py-1.5 text-sm font-medium rounded-md whitespace-nowrap ${activeTab === filter.id
                    ? 'bg-orange-100 text-orange-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
              >
                {filter.label}
                {filter.id === 'all' && (
                  <span className="ml-1 px-1.5 py-0.5 bg-gray-200 rounded-full text-xs">
                    {totalCount}
                  </span>
                )}
              </button>
            ))}
          </div>

          <div className="flex items-center space-x-3">
            {/* Download Button with Dropdown */}
            <div className="relative inline-block text-left">
              <div className="flex">
                <button
                  onClick={() => setShowDateRangeModal(true)}

                  disabled={isLoading}
                  className="flex items-center px-4 py-2 bg-green-600 text-white rounded-l-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Download all orders as Excel file"
                >
                  <span className="material-icons-round mr-2">download</span>
                  <span className="hidden sm:inline">Download XLSX</span>
                  <span className="sm:hidden">Export</span>
                </button>
                <button
                  onClick={() => setShowDateRangeModal(true)}
                  disabled={isLoading}
                  className="flex items-center px-3 py-2 bg-green-600 text-white rounded-r-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed border-l border-green-500"
                  title="Download orders by date range"
                >
                  <span className="material-icons-round">date_range</span>
                </button>
              </div>
            </div>

            {/* Search Box */}
            <div className="relative">
              <input
                type="text"
                placeholder="Search by order # or customer..."
                value={searchTerm}
                onChange={handleSearch}
                className="w-full md:w-80 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
              <span className="absolute right-3 top-2 text-gray-400">
                <span className="material-icons-round">search</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <DataTable
        columns={columns}
        data={orders}
        isLoading={isLoading}
        pagination={true}
        paginationConfig={{
          currentPage: currentPage,
          totalItems: totalCount,
          itemsPerPage: ITEMS_PER_PAGE,
          totalPages: Math.ceil(totalCount / ITEMS_PER_PAGE),
          onPageChange: handlePageChange
        }}
        emptyMessage="No orders found"
        actions={(row) => (
          <div className="flex items-center space-x-3 justify-end">
            <a
              href={`/admin/order/${row.id}`}
              className="text-orange-600 hover:text-orange-900 font-medium"
            >
              <span className="material-icons-round">visibility</span>
            </a>
            <button
              onClick={(e) => {
                e.preventDefault();
                openDeleteModal(row);
              }}
              className="text-red-600 hover:text-red-900 font-medium"
              title="Delete Order"
            >
              <span className="material-icons-round">delete</span>
            </button>
          </div>
        )}
      />
    </div>
  );
}